package service

import (
	"fmt"
	"navy-ng/models/portal"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	// 日志消息
	logEvaluatingStrategies    = "Starting to evaluate all enabled strategies"
	logFailedToFetchStrategies = "Failed to fetch enabled strategies"
	logLockNotAcquired         = "Strategy lock not acquired, another instance is likely evaluating"
	logStrategyInCooldown      = "Strategy skipped due to cooldown period"
	logNoAssociatedClusters    = "No clusters associated with strategy, skipping"

	// 错误信息
	errFailedToCheckLock       = "failed to check redis lock for strategy %d: %w"
	errFailedToCheckCooldown   = "failed to check cooldown for strategy %d: %w"
	errFailedToGetAssociations = "failed to get associations for strategy %d: %w"

	// Redis 锁
	lockKeyFormat   = "elastic_scaling:strategy:%d:lock"
	lockValueFormat = "eval:%d:%d"

	// Zap 日志字段键
	zapKeyStrategyID = "strategyID"

	// GORM 查询
	queryStatusEnabled       = "status = ?"
	queryStrategyIDAndResult = "strategy_id = ? AND result = ?"
	queryResourceTypeAndPool = "resource_type = ? AND resource_pool = ?"
	resultOrderCreated       = "order_created"
	orderByExecutionTimeDesc = "execution_time DESC"

	// 资源类型
	resourceTypeTotal = "total"

	// 日期格式
	dateFormat = "2006-01-02"
)

// EvaluateStrategies 评估所有启用的策略，并可能创建订单。
// 该函数是策略评估的入口点，通常由定时任务调用。
func (s *ElasticScalingService) EvaluateStrategies() error {
	s.logger.Info(logEvaluatingStrategies)
	var strategies []portal.ElasticScalingStrategy
	if err := s.db.Where(queryStatusEnabled, portal.StrategyStatusEnabled).Find(&strategies).Error; err != nil {
		s.logger.Error(logFailedToFetchStrategies, zap.Error(err))
		return err
	}
	s.logger.Info("Fetched enabled strategies", zap.Int("count", len(strategies)))

	for _, strategy := range strategies {
		// 为每个策略单独评估，记录错误但继续处理其他策略
		if err := s.evaluateStrategy(&strategy); err != nil {
			s.logger.Error("Error evaluating strategy",
				zap.Int64("strategyID", strategy.ID),
				zap.String("strategyName", strategy.Name),
				zap.Error(err))
		}
	}
	return nil
}

// evaluateStrategy 评估单个策略的完整流程。
// 它负责锁、冷却期检查、数据获取和触发评估。
func (s *ElasticScalingService) evaluateStrategy(strategy *portal.ElasticScalingStrategy) error {
	s.logger.Info("Starting single strategy evaluation", zap.Int64("strategyID", strategy.ID), zap.String("strategyName", strategy.Name))

	// 1. 尝试获取分布式锁
	lockKey := fmt.Sprintf(lockKeyFormat, strategy.ID)
	lockValue := fmt.Sprintf(lockValueFormat, strategy.ID, time.Now().UnixNano())
	locked, err := s.redisHandler.AcquireLock(lockKey, lockValue, 30*time.Second)
	if err != nil {
		return fmt.Errorf(errFailedToCheckLock, strategy.ID, err)
	}
	if !locked {
		s.logger.Info(logLockNotAcquired, zap.Int64(zapKeyStrategyID, strategy.ID))
		return nil
	}
	defer s.redisHandler.Delete(lockKey)

	// 2. 检查冷却期
	inCooldown, err := s.isStrategyInCooldown(strategy)
	if err != nil {
		return fmt.Errorf(errFailedToCheckCooldown, strategy.ID, err)
	}
	if inCooldown {
		s.logger.Info(logStrategyInCooldown, zap.Int64(zapKeyStrategyID, strategy.ID))
		return nil
	}

	// 3. 获取关联集群
	associations, err := s.getStrategyClusterAssociations(strategy.ID)
	if err != nil {
		return fmt.Errorf(errFailedToGetAssociations, strategy.ID, err)
	}
	if len(associations) == 0 {
		s.logger.Warn(logNoAssociatedClusters, zap.Int64(zapKeyStrategyID, strategy.ID))
		return nil
	}

	// 4. 循环评估每个关联
	for _, assoc := range associations {
		s.evaluateAssociation(strategy, assoc.ClusterID)
	}

	return nil
}

// evaluateAssociation 评估策略与单个集群的关联。
func (s *ElasticScalingService) evaluateAssociation(strategy *portal.ElasticScalingStrategy, clusterID int64) {
	resourceTypes := parseResourceTypes(strategy.ResourceTypes)

	for _, resourceType := range resourceTypes {
		s.logger.Info("Evaluating for resource type",
			zap.Int64("strategyID", strategy.ID),
			zap.Int64("clusterID", clusterID),
			zap.String("resourceType", resourceType))

		// 获取每日快照
		daysToCheck := 7 // 默认检查7天
		snapshots, err := s.getOrderedDailySnapshots(clusterID, resourceType, daysToCheck)
		if err != nil {
			s.logger.Error("Failed to get daily snapshots", zap.Error(err), zap.Int64("clusterID", clusterID))
			continue
		}

		if len(snapshots) == 0 {
			logMsg := fmt.Sprintf("No resource snapshots found for cluster %d, resource type %s within the last %d days.", clusterID, resourceType, daysToCheck)
			s.logger.Info(logMsg, zap.Int64("strategyID", strategy.ID))
			currentTime := portal.NavyTime(time.Now())
			s.recordStrategyExecution(strategy.ID, clusterID, resourceType, StrategyExecutionResultFailureNoSnapshots, nil, logMsg, "", "", &currentTime)
			continue
		}

		// 核心评估逻辑
		breached, consecutiveDays, triggeredValue, thresholdValue := s.EvaluateSnapshots(snapshots, strategy)

		// 根据评估结果执行操作
		requiredDays := getRequiredConsecutiveDays(strategy)
		currentTime := portal.NavyTime(time.Now())
		if breached {
			s.logger.Info("Threshold consistently breached for strategy",
				zap.Int64("strategyID", strategy.ID),
				zap.Int64("clusterID", clusterID),
				zap.Int("consecutiveDays", consecutiveDays),
				zap.Int("requiredDays", requiredDays))

			// 计算资源增量
			cpuDelta, memDelta := s.calculateResourceDelta(snapshots[len(snapshots)-1], strategy)

			s.logger.Info("Calculated resource delta",
				zap.Int64("strategyID", strategy.ID),
				zap.Int64("clusterID", clusterID),
				zap.Float64("cpuDelta", cpuDelta),
				zap.Float64("memDelta", memDelta))

			// 触发设备匹配和订单创建
			if err := s.matchDevicesForStrategyFunc(strategy, clusterID, resourceType, triggeredValue, thresholdValue, cpuDelta, memDelta); err != nil {
				s.logger.Error("Error during device matching for strategy", zap.Error(err), zap.Int64("strategyID", strategy.ID))
			}
		} else {
			s.logger.Info("Threshold not consistently breached for strategy",
				zap.Int64("strategyID", strategy.ID),
				zap.Int64("clusterID", clusterID),
				zap.Int("consecutiveDays", consecutiveDays),
				zap.Int("requiredDays", requiredDays))

			reason := fmt.Sprintf("Threshold not consistently met for cluster %d and resource type %s for %d consecutive days (required: %d days).",
				clusterID, resourceType, consecutiveDays, requiredDays)
			s.recordStrategyExecution(strategy.ID, clusterID, resourceType, StrategyExecutionResultFailureThresholdNotMet, nil, reason, triggeredValue, thresholdValue, &currentTime)
		}
	}
}

// isStrategyInCooldown 检查策略是否处于冷却期。
func (s *ElasticScalingService) isStrategyInCooldown(strategy *portal.ElasticScalingStrategy) (bool, error) {
	var latestHistory portal.StrategyExecutionHistory
	err := s.db.Where(queryStrategyIDAndResult, strategy.ID, resultOrderCreated).
		Order(orderByExecutionTimeDesc).
		First(&latestHistory).Error

	if err != nil {
		// 如果没有找到记录，则不在冷却期
		if isGormRecordNotFoundError(err) {
			return false, nil
		}
		return false, err
	}

	var cooldownEndTime time.Time
	latestHistory.ExecutionTime.Scan(&cooldownEndTime)
	cooldownEndTime = cooldownEndTime.Add(time.Duration(strategy.CooldownMinutes) * time.Minute)

	return time.Now().Before(cooldownEndTime), nil
}

// getStrategyClusterAssociations 获取策略关联的集群。
func (s *ElasticScalingService) getStrategyClusterAssociations(strategyID int64) ([]portal.StrategyClusterAssociation, error) {
	var associations []portal.StrategyClusterAssociation
	if err := s.db.Where("strategy_id = ?", strategyID).Find(&associations).Error; err != nil {
		return nil, err
	}
	return associations, nil
}

// getOrderedDailySnapshots 获取并处理每日资源快照。
func (s *ElasticScalingService) getOrderedDailySnapshots(clusterID int64, resourceType string, days int) ([]portal.ResourceSnapshot, error) {
	startDate := time.Now().AddDate(0, 0, -days)
	query := s.db.Where("cluster_id = ? AND created_at >= ?", clusterID, startDate)

	if resourceType != resourceTypeTotal {
		query = query.Where(queryResourceTypeAndPool, resourceType, resourceType)
	}

	var snapshots []portal.ResourceSnapshot
	if err := query.Order(OrderByCreatedAtDesc).Find(&snapshots).Error; err != nil {
		return nil, err
	}

	// 按天分组，每天只取最新的一个快照
	dailySnapshotMap := make(map[string]portal.ResourceSnapshot)
	for _, snapshot := range snapshots {
		day := time.Time(snapshot.CreatedAt).Format(dateFormat)
		if _, exists := dailySnapshotMap[day]; !exists {
			dailySnapshotMap[day] = snapshot
		}
	}

	var orderedDailySnapshots []portal.ResourceSnapshot
	for _, snapshot := range dailySnapshotMap {
		orderedDailySnapshots = append(orderedDailySnapshots, snapshot)
	}

	// 按创建时间升序排序
	sort.Slice(orderedDailySnapshots, func(i, j int) bool {
		return time.Time(orderedDailySnapshots[i].CreatedAt).Before(time.Time(orderedDailySnapshots[j].CreatedAt))
	})

	return orderedDailySnapshots, nil
}

// EvaluateSnapshots 是核心评估逻辑，无副作用，易于测试。
// 它接收快照和策略，返回是否触发、连续天数以及相关的监控值。
func (s *ElasticScalingService) EvaluateSnapshots(
	snapshots []portal.ResourceSnapshot,
	strategy *portal.ElasticScalingStrategy,
) (breached bool, consecutiveDays int, triggeredValueStr string, thresholdValueStr string) {
	var maxConsecutiveDays int
	var lastBreachedTriggeredValue string
	var lastBreachedThresholdValue string

	for _, snapshot := range snapshots {
		singleBreached, singleTriggeredValue, singleThresholdValue := s.checkSingleSnapshotBreach(snapshot, strategy)

		if singleBreached {
			consecutiveDays++
			lastBreachedTriggeredValue = singleTriggeredValue
			lastBreachedThresholdValue = singleThresholdValue
		} else {
			// 更新最大连续天数并重置计数器
			if consecutiveDays > maxConsecutiveDays {
				maxConsecutiveDays = consecutiveDays
			}
			consecutiveDays = 0
		}
	}
	// 循环结束后再次更新最大连续天数
	if consecutiveDays > maxConsecutiveDays {
		maxConsecutiveDays = consecutiveDays
	}

	requiredDays := getRequiredConsecutiveDays(strategy)
	// 只有当评估周期末尾的连续天数满足条件时才算触发
	if consecutiveDays >= requiredDays {
		return true, consecutiveDays, lastBreachedTriggeredValue, lastBreachedThresholdValue
	}

	// 如果未触发，返回观察到的最大连续天数和最后一次触发时的值
	return false, maxConsecutiveDays, lastBreachedTriggeredValue, lastBreachedThresholdValue
}

// checkSingleSnapshotBreach 检查单个快照是否满足策略阈值。
func (s *ElasticScalingService) checkSingleSnapshotBreach(snapshot portal.ResourceSnapshot, strategy *portal.ElasticScalingStrategy) (
	breached bool, triggeredValueStr string, thresholdValueStr string) {

	var cpuMet, memMet bool
	var cpuVal, memVal float64 = -1.0, -1.0

	// CPU检查
	if strategy.CPUThresholdValue > 0 {
		if strategy.CPUThresholdType == ThresholdTypeUsage {
			cpuVal = snapshot.MaxCpuUsageRatio
		} else {
			cpuVal = safePercentage(snapshot.CpuRequest, snapshot.CpuCapacity)
		}
		cpuMet = compare(cpuVal, float64(strategy.CPUThresholdValue), strategy.ThresholdTriggerAction)
	} else {
		cpuMet = true // 没有定义CPU阈值，则默认满足
	}

	// 内存检查
	if strategy.MemoryThresholdValue > 0 {
		if strategy.MemoryThresholdType == ThresholdTypeUsage {
			memVal = snapshot.MaxMemoryUsageRatio
		} else {
			memVal = safePercentage(snapshot.MemRequest, snapshot.MemoryCapacity)
		}
		memMet = compare(memVal, float64(strategy.MemoryThresholdValue), strategy.ThresholdTriggerAction)
	} else {
		memMet = true // 没有定义内存阈值，则默认满足
	}

	// 逻辑组合
	if strategy.CPUThresholdValue > 0 && strategy.MemoryThresholdValue > 0 {
		if strategy.ConditionLogic == ConditionLogicAnd {
			breached = cpuMet && memMet
		} else {
			breached = cpuMet || memMet
		}
	} else if strategy.CPUThresholdValue > 0 {
		breached = cpuMet
	} else if strategy.MemoryThresholdValue > 0 {
		breached = memMet
	} else {
		breached = false // 策略无效
	}

	triggeredValueStr = s.buildTriggeredValueString(cpuVal, memVal, strategy)
	thresholdValueStr = s.buildThresholdString(strategy)

	return breached, triggeredValueStr, thresholdValueStr
}

// calculateResourceDelta 计算需要调整的资源量
func (s *ElasticScalingService) calculateResourceDelta(latestSnapshot portal.ResourceSnapshot, strategy *portal.ElasticScalingStrategy) (cpuDelta float64, memDelta float64) {
	// 目标是降低到阈值水平，所以我们需要计算超出的部分
	// 入池：需要增加的资源 = (当前值 - 阈值) * 总容量 / 阈值
	// 出池：需要减少的资源 = (阈值 - 当前值) * 总容量 / 阈值
	// 注意：这里的计算是简化的，实际场景可能更复杂

	if strategy.ThresholdTriggerAction == TriggerActionPoolEntry {
		if strategy.CPUThresholdValue > 0 {
			currentCPUUsage := safePercentage(latestSnapshot.CpuRequest, latestSnapshot.CpuCapacity)
			if currentCPUUsage > strategy.CPUThresholdValue {
				// 我们希望将利用率降至目标值，例如阈值本身
				targetCPUUsage := strategy.CPUThresholdValue
				// (currentUsage/total - targetUsage/total) * total = (currentUsage - targetUsage)
				// (currentRequest/currentCapacity - targetRequest/newCapacity)
				// 假设 targetRequest = currentRequest, 求解 newCapacity
				// currentRequest / targetCPUUsage = newCapacity
				newCapacity := latestSnapshot.CpuRequest / (targetCPUUsage / 100)
				cpuDelta = newCapacity - latestSnapshot.CpuCapacity
			}
		}
		if strategy.MemoryThresholdValue > 0 {
			currentMemUsage := safePercentage(latestSnapshot.MemRequest, latestSnapshot.MemoryCapacity)
			if currentMemUsage > strategy.MemoryThresholdValue {
				targetMemUsage := strategy.MemoryThresholdValue
				newCapacity := latestSnapshot.MemRequest / (targetMemUsage / 100)
				memDelta = newCapacity - latestSnapshot.MemoryCapacity
			}
		}
	} else if strategy.ThresholdTriggerAction == TriggerActionPoolExit {
		if strategy.CPUThresholdValue > 0 {
			currentCPUUsage := safePercentage(latestSnapshot.CpuRequest, latestSnapshot.CpuCapacity)
			if currentCPUUsage < strategy.CPUThresholdValue {
				// 我们希望将利用率提升至目标值
				targetCPUUsage := strategy.CPUThresholdValue
				// 假设 targetRequest = currentRequest, 求解 newCapacity
				// currentRequest / targetCPUUsage = newCapacity
				newCapacity := latestSnapshot.CpuRequest / (targetCPUUsage / 100)
				cpuDelta = newCapacity - latestSnapshot.CpuCapacity // Delta will be negative
			}
		}
		if strategy.MemoryThresholdValue > 0 {
			currentMemUsage := safePercentage(latestSnapshot.MemRequest, latestSnapshot.MemoryCapacity)
			if currentMemUsage < strategy.MemoryThresholdValue {
				targetMemUsage := strategy.MemoryThresholdValue
				newCapacity := latestSnapshot.MemRequest / (targetMemUsage / 100)
				memDelta = newCapacity - latestSnapshot.MemoryCapacity // Delta will be negative
			}
		}
	}

	return cpuDelta, memDelta
}

// compare 辅助函数，根据扩容或缩容操作比较值。
func compare(current, threshold float64, action string) bool {
	if action == TriggerActionPoolEntry { // 扩容：当前值 > 阈值
		return current > threshold
	}
	return current < threshold // 缩容：当前值 < 阈值
}

// parseResourceTypes 解析资源类型字符串。
func parseResourceTypes(resourceTypesStr string) []string {
	if resourceTypesStr == "" {
		return []string{"total"}
	}
	types := strings.Split(resourceTypesStr, ",")
	for i, rt := range types {
		types[i] = strings.TrimSpace(rt)
	}
	return types
}

// getRequiredConsecutiveDays 从策略中计算需要的天数。
func getRequiredConsecutiveDays(strategy *portal.ElasticScalingStrategy) int {
	// DurationMinutes 字段可能被误用为天数，这里做兼容处理
	// 假设如果值小于100，它代表天数；否则代表分钟
	if strategy.DurationMinutes > 0 && strategy.DurationMinutes < 100 {
		return strategy.DurationMinutes
	}
	days := strategy.DurationMinutes / (24 * 60)
	if days < 1 {
		return 1 // 至少1天
	}
	return days
}

// buildThresholdString 构建策略阈值的字符串表示。
func (s *ElasticScalingService) buildThresholdString(strategy *portal.ElasticScalingStrategy) string {
	var parts []string
	actionStr := ">"
	if strategy.ThresholdTriggerAction == TriggerActionPoolExit {
		actionStr = "<"
	}

	if strategy.CPUThresholdValue > 0 {
		parts = append(parts, fmt.Sprintf("CPU %s %s %.2f%%", strategy.CPUThresholdType, actionStr, strategy.CPUThresholdValue))
	}
	if strategy.MemoryThresholdValue > 0 {
		parts = append(parts, fmt.Sprintf("Memory %s %s %.2f%%", strategy.MemoryThresholdType, actionStr, strategy.MemoryThresholdValue))
	}

	logic := " "
	if len(parts) > 1 {
		logic = fmt.Sprintf(" %s ", strategy.ConditionLogic)
	}

	return fmt.Sprintf("%s for %d days", strings.Join(parts, logic), getRequiredConsecutiveDays(strategy))
}

// buildTriggeredValueString 构建实际触发值的字符串表示。
func (s *ElasticScalingService) buildTriggeredValueString(cpuValue, memValue float64, strategy *portal.ElasticScalingStrategy) string {
	var parts []string
	if strategy.CPUThresholdValue > 0 {
		if cpuValue >= 0 {
			parts = append(parts, fmt.Sprintf("CPU %s: %.2f%%", strategy.CPUThresholdType, cpuValue))
		} else {
			parts = append(parts, fmt.Sprintf("CPU %s: N/A", strategy.CPUThresholdType))
		}
	}

	if strategy.MemoryThresholdValue > 0 {
		if memValue >= 0 {
			parts = append(parts, fmt.Sprintf("Memory %s: %.2f%%", strategy.MemoryThresholdType, memValue))
		} else {
			parts = append(parts, fmt.Sprintf("Memory %s: N/A", strategy.MemoryThresholdType))
		}
	}

	if len(parts) == 0 {
		return "No relevant metrics recorded"
	}
	return strings.Join(parts, ", ")
}

// recordStrategyExecution 记录策略执行历史。
func (s *ElasticScalingService) recordStrategyExecution(
	strategyID int64,
	clusterID int64,
	resourceType string,
	result string,
	orderID *int64,
	reason string,
	triggeredValue string,
	thresholdValue string,
	specificExecutionTime *portal.NavyTime,
) error {
	execTime := portal.NavyTime(time.Now())
	if specificExecutionTime != nil {
		execTime = *specificExecutionTime
	}

	history := portal.StrategyExecutionHistory{
		StrategyID:     strategyID,
		ClusterID:      clusterID,
		ResourceType:   resourceType,
		ExecutionTime:  execTime,
		TriggeredValue: triggeredValue,
		ThresholdValue: thresholdValue,
		Result:         result,
		OrderID:        orderID,
		Reason:         reason,
	}

	if err := s.db.Create(&history).Error; err != nil {
		s.logger.Error("Failed to create strategy execution history entry in DB", zap.Error(err), zap.Int64("strategyID", strategyID))
		return err
	}
	return nil
}

// isGormRecordNotFoundError 检查错误是否为gorm.ErrRecordNotFound
func isGormRecordNotFoundError(err error) bool {
	return err != nil && err == gorm.ErrRecordNotFound
}
