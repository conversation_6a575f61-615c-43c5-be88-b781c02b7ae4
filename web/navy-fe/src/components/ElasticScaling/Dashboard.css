.dashboard {
  padding: 0;
  background-color: #f0f2f5;
}

/* 页面头部样式 */
.page-header {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.header-title {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  position: relative;
  letter-spacing: 0.5px;
}

.header-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #f0f0f0;
}

.header-icon {
  font-size: 18px;
  margin-right: 10px;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统计卡片容器 */
.stats-cards {
  margin-bottom: 24px;
}

/* 统计卡片样式 */
.stat-card {
  background: #fff;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transform: translateY(-2px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: #1890ff;
}

.stat-card.warning::before {
  background-color: #faad14;
}

.stat-card.success::before {
  background-color: #52c41a;
}

.stat-card.error::before {
  background-color: #f5222d;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  line-height: 1.4;
}

.stat-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 24px;
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.trend-up {
  color: #f5222d;
}

.trend-down {
  color: #52c41a;
}

/* 订单卡片样式 */
.order-card {
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 16px;
  border-left: 3px solid #1890ff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s;
  cursor: pointer;
}

.order-card.pool-in {
  border-left-color: #1890ff;
}

.order-card.pool-out {
  border-left-color: #faad14;
}

.order-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.order-card-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.order-card-title {
  font-weight: 600;
  font-size: 15px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 48px;
  line-height: 1.4;
}

.order-card-body {
  padding: 16px 24px;
}

.order-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  width: 100%;
}

.order-meta-item {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: left;
  padding: 8px 16px;
  background: rgba(24, 144, 255, 0.04);
  border-radius: 6px;
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.order-meta-item:hover {
  background: rgba(24, 144, 255, 0.08);
  border-color: rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.order-meta-label {
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
  margin-right: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.order-meta-label::before {
  content: '';
  width: 3px;
  height: 3px;
  background: #1890ff;
  border-radius: 50%;
  display: inline-block;
}

.order-meta-value {
  color: rgba(0, 0, 0, 0.88);
  font-weight: 700;
  font-size: 14px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.order-meta-value .anticon {
  color: #1890ff;
  font-size: 14px;
}

.order-card-footer {
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

/* 订单卡片按钮组样式 */
.order-card-footer .ant-space {
  flex: 1;
}

.order-card-footer .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

.order-card-footer .ant-btn-link {
  color: #1890ff;
  border: 1px solid transparent;
  background: rgba(24, 144, 255, 0.06);
  box-shadow: none;
}

.order-card-footer .ant-btn-link:hover {
  background: rgba(24, 144, 255, 0.12);
  border-color: rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.order-card-footer .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.order-card-footer .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.order-card-footer .ant-btn-dangerous {
  background: rgba(255, 77, 79, 0.06);
  border-color: rgba(255, 77, 79, 0.2);
  color: #ff4d4f;
}

.order-card-footer .ant-btn-dangerous:hover {
  background: rgba(255, 77, 79, 0.12);
  border-color: rgba(255, 77, 79, 0.4);
  transform: translateY(-1px);
}

/* 操作按钮组右对齐 */
.order-card-footer .order-action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-left: auto;
}

/* 策略表格样式 */
.strategy-table .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 操作按钮样式 - 与设备匹配策略保持一致 */
.strategy-table .action-buttons {
  display: flex;
  justify-content: center;
}

.strategy-table .ant-btn-text {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.3s;
  margin: 0 4px;
  position: relative;
}

.strategy-table .ant-btn-text:hover {
  background-color: #f5f5f5;
}

.strategy-table .ant-btn-text[danger]:hover {
  background-color: #fff1f0;
}

.strategy-table .ant-btn-text .anticon {
  font-size: 16px;
}

/* 编辑按钮 */
.strategy-table .edit-button .anticon {
  color: #1890ff;
}

.strategy-table .edit-button:hover {
  background-color: #e6f7ff;
}

/* 启用按钮 */
.strategy-table .enable-button .anticon {
  color: #52c41a;
}

.strategy-table .enable-button:hover {
  background-color: #f6ffed;
}

/* 禁用按钮 */
.strategy-table .disable-button .anticon {
  color: #ff4d4f;
}

.strategy-table .disable-button:hover {
  background-color: #fff1f0;
}

/* 执行历史按钮 */
.strategy-table .history-button .anticon {
  color: #722ed1;
}

.strategy-table .history-button:hover {
  background-color: #f9f0ff;
}

/* 删除按钮 */
.strategy-table .delete-button .anticon {
  color: #ff4d4f;
}

.strategy-table .delete-button:hover {
  background-color: #fff1f0;
}

/* 策略详情抽屉 */
.detail-drawer {
  z-index: 1001 !important;
}

.detail-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-drawer .ant-drawer-header .ant-drawer-title {
  color: #fff;
  font-weight: 600;
  font-size: 16px;
}

.detail-drawer .ant-drawer-close {
  color: #fff;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.detail-drawer .ant-drawer-close:hover {
  opacity: 1;
}

.detail-drawer .ant-drawer-body {
  padding: 0;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

.detail-drawer-header {
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.detail-drawer-content {
  padding: 0 24px 24px 24px;
  background: transparent;
}

.detail-section {
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.detail-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.detail-section .ant-descriptions {
  margin-bottom: 0;
}

.detail-section .ant-descriptions-bordered .ant-descriptions-item-label {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 500;
  color: rgba(0, 0, 0, 0.75);
  border-right: 1px solid #e8f4fd;
}

.detail-section .ant-descriptions-bordered .ant-descriptions-item-content {
  background-color: #fff;
  padding: 12px 16px;
}

.detail-section .ant-descriptions-bordered .ant-descriptions-row {
  border-bottom: 1px solid #f0f5ff;
}

.detail-section .ant-descriptions-bordered .ant-descriptions-row:last-child {
  border-bottom: none;
}

.detail-section-title {
  font-weight: 600;
  margin-bottom: 16px;
  padding: 20px 24px;
  border-bottom: 1px solid #e8f4fd;
  background: linear-gradient(135deg, #f8fafc 0%, #e8f4fd 100%);
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  font-size: 15px;
}

.detail-section-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 18px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  margin-right: 12px;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

/* 策略执行历史抽屉样式 */
.ant-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-drawer .ant-drawer-header .ant-drawer-title {
  color: #fff;
  font-weight: 600;
  font-size: 16px;
}

.ant-drawer .ant-drawer-close {
  color: #fff;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.ant-drawer .ant-drawer-close:hover {
  opacity: 1;
}

.ant-drawer .ant-drawer-body {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 24px;
}

.ant-drawer .ant-table-wrapper {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.ant-drawer .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #e8f4fd 100%);
  border-bottom: 1px solid #e8f4fd;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.ant-drawer .ant-table-tbody > tr:hover > td {
  background-color: #f0f9ff;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
  width: 100%;
}

/* 空状态美化 */
.content-card {
  border-radius: 4px !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03) !important;
  margin-bottom: 20px;
}

.content-card .ant-card-head {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.content-card .ant-card-head-title {
  font-size: 15px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  letter-spacing: 0.3px;
  padding: 14px 0;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  min-height: 220px;
  background-color: #fafafa;
  border-radius: 4px;
  text-align: center;
}

.empty-container .ant-empty-image {
  height: 100px;
  margin-bottom: 16px;
}

.empty-container .ant-empty-description {
  color: rgba(0, 0, 0, 0.45);
  font-size: 15px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.empty-container .empty-action {
  margin-top: 16px;
}

.empty-container .empty-icon {
  font-size: 28px;
  color: #1890ff;
  margin-bottom: 16px;
  background: rgba(24, 144, 255, 0.1);
  padding: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72px;
  height: 72px;
}

/* 设备列表样式 */
.device-list {
  margin-top: 8px;
}

.device-item {
  padding: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  margin-bottom: 12px;
  border: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
}

.device-item:last-child {
  margin-bottom: 0;
}

.device-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transform: translateY(-2px);
  border-color: #e6f7ff;
  background-color: #f0f9ff;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-name {
  font-weight: 600;
  font-size: 15px;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 6px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 13px;
}

.device-meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.device-meta-label {
  color: rgba(0, 0, 0, 0.45);
  font-weight: 500;
}

.device-status {
  padding: 2px 10px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.status-special {
  background-color: #fff2e8;
  color: #fa541c;
  border: 1px solid #ffbb96;
}

.status-in-cluster {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-available {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 订单标签页样式 */
.order-tabs .ant-tabs-nav {
  margin-bottom: 16px;
  background: #fafafa;
  padding: 8px 16px;
  border-radius: 8px;
}

.order-tabs .ant-tabs-tab {
  padding: 12px 16px;
  margin: 0 8px 0 0;
  transition: all 0.3s;
  border-radius: 4px;
}

.order-tabs .ant-tabs-tab:hover {
  background: #f0f0f0;
}

.order-tabs .ant-tabs-tab-active {
  background: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.order-tabs .ant-tabs-content {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.order-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

/* 响应式调整：确保在不同屏幕尺寸下都能合理显示 */
@media (min-width: 1400px) {
  .order-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 1200px) {
  .order-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 992px) {
  .order-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .order-cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.order-status-summary {
  display: flex;
  padding: 16px;
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-status-item {
  text-align: center;
  flex: 1;
  max-width: 20%;
}

.order-status-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.order-status-label {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.order-status-pending {
  color: #f5222d;
}

.order-status-processing {
  color: #1890ff;
}

.order-status-done {
  color: #52c41a;
}

.order-status-cancelled {
  color: #8c8c8c;
}

.order-count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 20px;
  white-space: nowrap;
  background: #f5f5f5;
  border-radius: 10px;
  margin-left: 8px;
  transition: all 0.3s;
}

.order-count-badge.pending {
  background: #fff1f0;
  color: #f5222d;
}

.order-count-badge.processing {
  background: #e6f7ff;
  color: #1890ff;
}

.order-count-badge.done {
  background: #f6ffed;
  color: #52c41a;
}

.order-count-badge.all {
  background: #f5f5f5;
  color: #8c8c8c;
}

.order-count-badge.cancelled {
  background: #f0f0f0;
  color: #8c8c8c;
}

.order-count-badge.custom {
  background: #fff7e6;
  color: #fa8c16;
}

/* 编辑策略模态框样式 */
.edit-strategy-modal .ant-modal-content {
  transition: all 0.3s ease-in-out;
  transform-origin: center top;
  animation: modalFadeIn 0.3s;
}

.edit-strategy-modal .ant-modal-body {
  transition: opacity 0.3s ease-in-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 资源使用信息卡片 */
.resource-info-card {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.resource-info-card .resource-header {
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 12px;
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
}

.resource-info-card .resource-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.resource-info-card .resource-item {
  margin-bottom: 12px;
}

.resource-info-card .resource-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.resource-info-card .resource-item-header span:first-child {
  font-size: 11px;
  color: rgba(0, 0, 0, 0.65);
  font-weight: 500;
}

.resource-info-card .resource-item-header span:last-child {
  font-size: 11px;
  font-weight: 500;
}

/* 资源信息卡片中的进度条样式调整 */
.resource-info-card .ant-progress {
  margin-bottom: 0;
}

.resource-info-card .ant-progress-line {
  font-size: 10px;
}

.resource-info-card .ant-progress-bg {
  height: 6px !important;
}

.resource-info-card .ant-progress-success-bg,
.resource-info-card .ant-progress-bg {
  height: 6px !important;
}

.chart-container {
  width: 100%;
  height: 350px;
  position: relative;
}

/* 空图表样式 */
.empty-chart-container {
  width: 100%;
  height: 350px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px dashed #e8e8e8;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.02);
}

.empty-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(to right, transparent 0%, transparent 49.9%, #f0f0f0 50%, #f0f0f0 50.1%, transparent 51%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, transparent 49.9%, #f0f0f0 50%, #f0f0f0 50.1%, transparent 51%, transparent 100%);
  background-size: 20px 20px;
  opacity: 0.3;
  z-index: 0;
}

.empty-chart-container .empty-chart-content {
  z-index: 1;
  text-align: center;
  padding: 24px 30px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.empty-chart-container:hover .empty-chart-content {
  transform: translateY(-3px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.empty-chart-container .empty-chart-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 16px;
  opacity: 0.7;
}

.empty-chart-container .empty-chart-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 8px;
}

.empty-chart-container .empty-chart-subtitle {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

/* 策略表单样式 */
.strategy-form .ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.85);
}

.strategy-form .ant-input,
.strategy-form .ant-input-number,
.strategy-form .ant-select-selector {
  border-radius: 4px;
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.strategy-form .ant-input:hover,
.strategy-form .ant-input-number:hover,
.strategy-form .ant-select-selector:hover {
  border-color: #40a9ff;
}

.strategy-form .ant-input:focus,
.strategy-form .ant-input-number-focused,
.strategy-form .ant-select-focused .ant-select-selector {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.strategy-form .ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.strategy-form .ant-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.strategy-form .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
  min-height: 40px;
  border-radius: 8px 8px 0 0;
}

.strategy-form .ant-card-head-title {
  padding: 10px 0;
}

.strategy-form .ant-select-selection-item {
  color: rgba(0, 0, 0, 0.85);
}

.strategy-form .ant-radio-button-wrapper {
  transition: all 0.3s;
}

.strategy-form .ant-radio-button-wrapper:first-child {
  border-radius: 4px 0 0 4px;
}

.strategy-form .ant-radio-button-wrapper:last-child {
  border-radius: 0 4px 4px 0;
}

.strategy-form .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background: #1890ff;
  border-color: #1890ff;
  color: #fff;
  box-shadow: -1px 0 0 0 #1890ff;
}

.strategy-form .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  background: #40a9ff;
  border-color: #40a9ff;
  color: #fff;
}

.strategy-form .ant-form-item-has-error .ant-input,
.strategy-form .ant-form-item-has-error .ant-input-number,
.strategy-form .ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
}

.strategy-form .ant-form-item-has-error .ant-input:focus,
.strategy-form .ant-form-item-has-error .ant-input-number-focused,
.strategy-form .ant-form-item-has-error .ant-select-focused .ant-select-selector {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 统一模态框样式 */
.create-strategy-modal,
.edit-strategy-modal,
.create-order-modal,
.policy-modal {
  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    background: #ffffff;
    border-radius: 6px 6px 0 0;
    position: relative;

    .ant-modal-title {
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      display: flex;
      align-items: center;
      position: relative;
      z-index: 2;
    }

    .ant-modal-close {
      position: absolute;
      top: 16px;
      right: 16px;
      z-index: 10;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(0, 0, 0, 0.06);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
      backdrop-filter: blur(4px);

      &:hover {
        background-color: #fff;
        border-color: rgba(255, 77, 79, 0.3);
        box-shadow: 0 4px 16px rgba(255, 77, 79, 0.2);
        transform: scale(1.1);
      }

      .ant-modal-close-x {
        width: 18px;
        height: 18px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.65);
        transition: all 0.3s ease;
        font-weight: 500;

        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }

  .ant-modal-body {
    padding: 24px;
    background-color: #f9fbfd;
    max-height: 70vh;
    overflow-y: auto;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    text-align: right;

    .ant-btn {
      height: 40px;
      padding: 8px 20px;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      margin-left: 12px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:first-child {
        margin-left: 0;
      }
    }

    .ant-btn-default {
      color: rgba(0, 0, 0, 0.65);
      border-color: #d9d9d9;
      background: #fff;

      &:hover {
        color: #40a9ff;
        border-color: #40a9ff;
        background: #f0f9ff;
      }
    }

    .ant-btn-primary {
      background: linear-gradient(135deg, #1890ff, #40a9ff);
      border: none;
      color: #fff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #40a9ff, #69c0ff);
        box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
      }

      &:disabled {
        background: #f5f5f5;
        color: rgba(0, 0, 0, 0.25);
        box-shadow: none;
        transform: none;
      }
    }
  }

  .ant-form-item-label > label {
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      border-color: #e6f7ff;
    }

    .ant-card-head {
      background-color: #f5f7fa;
      min-height: 48px;
      padding: 0 20px;
      border-bottom: 1px solid #f0f0f0;

      .ant-card-head-title {
        padding: 12px 0;
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .ant-card-body {
      padding: 20px 24px;
      background: #fff;
    }
  }

  .ant-input,
  .ant-select-selector,
  .ant-input-number,
  .ant-input-affix-wrapper {
    border-radius: 6px;
    border-color: #d9d9d9;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector {
      border-color: #40a9ff;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.2);
      outline: none;
    }
  }

  .ant-input-number-handler-wrap {
    border-radius: 0 6px 6px 0;
  }

  .ant-alert {
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid;
    font-size: 10px;
    padding: 8px 12px;

    &.ant-alert-info {
      background-color: #f0f9ff;
      border-color: #bae7ff;
    }
  }

  /* 订单卡片中的Alert样式调整 - 资源池状态信息作为辅助信息 */
  .order-card .ant-alert {
    font-size: 10px;
    padding: 4px 8px;
    margin-top: 8px;
    margin-bottom: 0;
    border-radius: 4px;
    opacity: 0.75;
  }

  .order-card .ant-alert .ant-alert-message {
    font-size: 10px;
    line-height: 1.3;
    color: rgba(0, 0, 0, 0.55);
    font-weight: 400;
  }

  .order-card .ant-alert .anticon {
    font-size: 10px;
    opacity: 0.8;
  }

  /* 资源池Alert的不同状态颜色调整 */
  .order-card .ant-alert.ant-alert-success {
    background-color: rgba(246, 255, 237, 0.8);
    border-color: rgba(183, 235, 143, 0.6);
  }

  .order-card .ant-alert.ant-alert-warning {
    background-color: rgba(255, 251, 230, 0.8);
    border-color: rgba(255, 229, 143, 0.6);
  }

  .order-card .ant-alert.ant-alert-error {
    background-color: rgba(255, 241, 240, 0.8);
    border-color: rgba(255, 163, 158, 0.6);
  }

  .order-card .ant-alert.ant-alert-info {
    background-color: rgba(240, 249, 255, 0.8);
    border-color: rgba(186, 231, 255, 0.6);
  }

  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-form-item-label {
    padding-bottom: 8px;
  }

  .ant-input {
    padding: 10px 12px;
    height: 40px;

    &::placeholder {
      color: rgba(0, 0, 0, 0.35);
    }
  }

  .ant-input-number {
    width: 100%;
    height: 40px;

    .ant-input-number-input {
      height: 38px;
      padding: 10px 12px;
    }
  }

  .ant-select {
    .ant-select-selector {
      height: 40px;
      padding: 6px 12px;

      .ant-select-selection-search-input {
        height: 28px;
      }

      .ant-select-selection-item {
        line-height: 28px;
        padding: 0;
      }

      .ant-select-selection-placeholder {
        line-height: 28px;
        color: rgba(0, 0, 0, 0.35);
      }
    }
  }

  .ant-select-multiple .ant-select-selector {
    padding: 4px 8px;
    min-height: 40px;

    .ant-select-selection-item {
      height: 28px;
      line-height: 26px;
      margin: 2px 4px 2px 0;
      padding: 0 8px;
      border-radius: 4px;
    }
  }

  .ant-radio-group {
    .ant-radio-button-wrapper {
      height: 40px;
      line-height: 38px;
      padding: 0 16px;
      border-radius: 0;
      transition: all 0.3s ease;

      &:first-child {
        border-radius: 6px 0 0 6px;
      }

      &:last-child {
        border-radius: 0 6px 6px 0;
      }

      &:hover {
        color: #40a9ff;
      }

      &.ant-radio-button-wrapper-checked {
        background: #1890ff;
        border-color: #1890ff;
        color: #fff;
        box-shadow: -1px 0 0 0 #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }

  .ant-checkbox-wrapper {
    .ant-checkbox {
      .ant-checkbox-inner {
        border-radius: 4px;
      }
      &.ant-checkbox-checked .ant-checkbox-inner {
        background-color: #1677ff;
        border-color: #1677ff;
      }
    }
  }
}

/* Custom styles for Ant Design Steps to improve vertical alignment */
.ant-steps-item-container {
  display: flex !important;
  align-items: center !important;
  min-height: 80px;
}

/* 统一调整订单状态流程步骤的样式 */
.order-status-flow {
  width: 100% !important;
  max-width: 800px !important;
  margin: 0 auto !important;
}

.order-status-flow .ant-steps-item-container {
  display: flex !important;
  align-items: center !important;
  min-height: 80px;
  padding: 0 20px !important;
}

.order-status-flow .ant-steps-item {
  flex: 1 !important;
  min-width: 120px !important;
}

.order-status-flow .ant-steps-item:first-child .ant-steps-item-container {
  padding-left: 24px !important;
}

.order-status-flow .ant-steps-item:last-child .ant-steps-item-container {
  padding-right: 24px !important;
}

.order-status-flow .ant-steps-item-tail {
  padding: 0 16px !important;
}

.order-status-flow .ant-steps-item-title {
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-top: 8px !important;
  text-align: center !important;
  white-space: nowrap !important;
}

.order-status-flow .ant-steps-item-icon {
  width: 32px !important;
  height: 32px !important;
  line-height: 32px !important;
  margin: 0 auto !important;
}
