-- 场景2：退池订单生成测试数据
-- 测试目标：验证当内存分配率连续2天低于20%时，系统能够正确生成退池订单

-- 清理现有数据
DELETE FROM strategy_execution_history;
DELETE FROM order_device;
DELETE FROM elastic_scaling_order_details;
DELETE FROM orders;
DELETE FROM k8s_cluster_resource_snapshot;
DELETE FROM strategy_cluster_association;
DELETE FROM elastic_scaling_strategy;
DELETE FROM query_template;
DELETE FROM device;
DELETE FROM k8s_cluster;

-- 重置自增ID
DELETE FROM sqlite_sequence WHERE name IN (
    'strategy_execution_history', 'order_device', 'elastic_scaling_order_details',
    'orders', 'k8s_cluster_resource_snapshot', 'strategy_cluster_association',
    'elastic_scaling_strategy', 'query_template', 'device', 'k8s_cluster'
);

-- 创建集群
INSERT INTO k8s_cluster (id, cluster_name, created_at, updated_at) VALUES
(2, 'staging-cluster', datetime('now'), datetime('now'));

-- 创建在池设备（可用于退池）
INSERT INTO device (id, ci_code, ip, arch_type, cpu, memory, status, role, cluster, cluster_id, is_special, feature_count, created_at, updated_at) VALUES
(4, 'DEV004', '************', 'x86_64', 8.0, 16.0, 'in_pool', 'worker', 'staging-cluster', 2, 0, 0, datetime('now'), datetime('now')),
(5, 'DEV005', '************', 'x86_64', 16.0, 32.0, 'in_pool', 'worker', 'staging-cluster', 2, 0, 0, datetime('now'), datetime('now')),
(6, 'DEV006', '************', 'arm64', 12.0, 24.0, 'in_pool', 'worker', 'staging-cluster', 2, 0, 0, datetime('now'), datetime('now'));

-- 创建查询模板（查找在池设备）
INSERT INTO query_template (id, name, groups, created_at, updated_at) VALUES
(2, 'Find Pool Devices', '[{"id":"1","blocks":[{"id":"2","type":"device","key":"status","conditionType":"equal","value":"in_pool"}],"operator":"AND"}]', datetime('now'), datetime('now'));

-- 创建弹性伸缩策略（退池策略）
INSERT INTO elastic_scaling_strategy (
    id, name, description, threshold_trigger_action,
    cpu_threshold_value, cpu_threshold_type, cpu_target_value,
    memory_threshold_value, memory_threshold_type, memory_target_value,
    condition_logic, duration_minutes, cooldown_minutes, device_count,
    node_selector, resource_types, status, exit_query_template_id,
    created_by, created_at, updated_at
) VALUES (
    2, 'Memory Low Usage Scale In', 'Scale in when memory allocation is low', 'pool_exit',
    0, '', 0,
    20.0, 'allocated', 30.0,
    'AND', 2, 60, 1,
    '', 'total', 'enabled', 2,
    'admin', datetime('now'), datetime('now')
);

-- 创建策略集群关联
INSERT INTO strategy_cluster_association (strategy_id, cluster_id, created_at, updated_at) VALUES
(2, 2, datetime('now'), datetime('now'));

-- 创建资源快照数据（连续2天内存分配率低于20%）
INSERT INTO k8s_cluster_resource_snapshot (
    cluster_id, resource_type, resource_pool,
    max_cpu_usage_ratio, max_memory_usage_ratio,
    cpu_request, cpu_capacity, mem_request, memory_capacity,
    created_at, updated_at
) VALUES
-- 3天前：内存分配率 25%（不满足）
(2, 'total', 'total', 45.0, 35.0, 450.0, 1000.0, 2500.0, 10000.0, datetime('now', '-3 days'), datetime('now', '-3 days')),
-- 2天前：内存分配率 15%（满足）
(2, 'total', 'total', 45.0, 35.0, 450.0, 1000.0, 1500.0, 10000.0, datetime('now', '-2 days'), datetime('now', '-2 days')),
-- 1天前：内存分配率 18%（满足）
(2, 'total', 'total', 40.0, 32.0, 400.0, 1000.0, 1800.0, 10000.0, datetime('now', '-1 days'), datetime('now', '-1 days'));

-- 模拟系统自动生成的退池订单数据
-- 创建基础订单
INSERT INTO orders (id, order_number, name, description, type, status, created_by, created_at, updated_at) VALUES
(2, 'ESO20241201234567', '策略触发-退池-staging-cluster-total', '策略 ''Memory Low Usage Scale In'' 触发退池操作。集群：staging-cluster，资源类型：total，涉及设备：1台。', 'elastic_scaling', 'pending', 'system/auto', datetime('now'), datetime('now'));

-- 创建弹性伸缩订单详情
INSERT INTO elastic_scaling_order_details (id, order_id, cluster_id, strategy_id, action_type, resource_pool_type, device_count, strategy_triggered_value, strategy_threshold_value, created_at, updated_at) VALUES
(2, 2, 2, 2, 'pool_exit', 'total', 1, '内存分配率: 18.0%', '内存阈值: 20.0%', datetime('now'), datetime('now'));

-- 创建订单设备关联（选择1台在池设备）
INSERT INTO order_device (order_id, device_id, status, created_at, updated_at) VALUES
(2, 4, 'pending', datetime('now'), datetime('now'));

-- 创建策略执行历史记录
INSERT INTO strategy_execution_history (id, strategy_id, cluster_id, resource_type, execution_time, triggered_value, threshold_value, result, order_id, reason, created_at, updated_at) VALUES
(2, 2, 2, 'total', datetime('now'), '内存分配率: 18.0%', '内存阈值: 20.0%', 'order_created', 2, '连续2天内存分配率低于20%，成功生成退池订单', datetime('now'), datetime('now'));

-- 验证数据插入
SELECT 'Clusters:' as table_name, count(*) as count FROM k8s_cluster
UNION ALL
SELECT 'Devices:', count(*) FROM device
UNION ALL
SELECT 'Query Templates:', count(*) FROM query_template
UNION ALL
SELECT 'Strategies:', count(*) FROM elastic_scaling_strategy
UNION ALL
SELECT 'Strategy Associations:', count(*) FROM strategy_cluster_association
UNION ALL
SELECT 'Resource Snapshots:', count(*) FROM k8s_cluster_resource_snapshot
UNION ALL
SELECT 'Orders:', count(*) FROM orders
UNION ALL
SELECT 'Order Details:', count(*) FROM elastic_scaling_order_details
UNION ALL
SELECT 'Order Devices:', count(*) FROM order_device
UNION ALL
SELECT 'Execution History:', count(*) FROM strategy_execution_history;

-- 显示策略详情
SELECT 
    id, name, threshold_trigger_action, 
    memory_threshold_value, memory_threshold_type,
    duration_minutes, status
FROM elastic_scaling_strategies;

-- 显示资源快照趋势（计算内存分配率）
SELECT 
    date(created_at) as snapshot_date,
    max_cpu_usage_ratio,
    max_memory_usage_ratio,
    mem_request,
    memory_capacity,
    ROUND((mem_request * 100.0 / memory_capacity), 2) as memory_allocation_ratio,
    CASE 
        WHEN (mem_request * 100.0 / memory_capacity) < 20 THEN 'BREACH'
        ELSE 'NORMAL'
    END as threshold_status
FROM resource_snapshots 
ORDER BY created_at;

-- 显示在池设备信息
SELECT 
    ci_code, ip, arch_type, 
    cpu, memory, status, cluster
FROM devices 
WHERE status = 'in_pool';
