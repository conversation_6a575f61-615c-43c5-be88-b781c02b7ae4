-- 场景7：退池部分设备匹配测试数据
-- 测试目标：验证当触发退池条件但只有部分设备可退池时，系统生成订单并匹配可用设备

-- 清理现有数据
DELETE FROM strategy_execution_history;
DELETE FROM order_device;
DELETE FROM elastic_scaling_order_details;
DELETE FROM orders;
DELETE FROM k8s_cluster_resource_snapshot;
DELETE FROM strategy_cluster_association;
DELETE FROM elastic_scaling_strategy;
DELETE FROM query_template;
DELETE FROM device;
DELETE FROM k8s_cluster;

-- 重置自增ID
DELETE FROM sqlite_sequence WHERE name IN (
    'strategy_execution_history', 'order_device', 'elastic_scaling_order_details',
    'orders', 'k8s_cluster_resource_snapshot', 'strategy_cluster_association',
    'elastic_scaling_strategy', 'query_template', 'device', 'k8s_cluster'
);

-- 创建集群
INSERT INTO k8s_cluster (id, clustername, created_at, updated_at) VALUES
(1, 'production-cluster', datetime('now'), datetime('now'));

-- 创建设备（部分运行中，部分其他状态）
INSERT INTO device (id, ci_code, ip, arch_type, cpu, memory, status, role, cluster, cluster_id, is_special, feature_count, created_at, updated_at) VALUES
(1001, 'DEV001', '************', 'x86_64', 8.0, 16.0, 'running', 'worker', 'production-cluster', 1, 0, 0, datetime('now'), datetime('now')),
(1002, 'DEV002', '************', 'x86_64', 16.0, 32.0, 'in_stock', 'worker', 'production-cluster', 1, 0, 0, datetime('now'), datetime('now')),
(1003, 'DEV003', '************', 'arm64', 12.0, 24.0, 'maintenance', 'worker', 'production-cluster', 1, 0, 0, datetime('now'), datetime('now')),
(1004, 'DEV004', '************', 'x86_64', 8.0, 16.0, 'running', 'worker', 'production-cluster', 1, 0, 0, datetime('now'), datetime('now'));

-- 创建查询模板（查找运行中的设备）
INSERT INTO query_template (id, name, groups, created_at, updated_at) VALUES
(1, 'Find Running Devices', '[{"id":"1","blocks":[{"id":"2","type":"device","key":"status","conditionType":"equal","value":"running"}],"operator":"AND"}]', datetime('now'), datetime('now'));

-- 创建弹性伸缩策略（退池策略，需要3台设备但只有2台可用）
INSERT INTO elastic_scaling_strategy (
    id, name, description, threshold_trigger_action,
    cpu_threshold_value, cpu_threshold_type, cpu_target_value,
    memory_threshold_value, memory_threshold_type, memory_target_value,
    condition_logic, duration_minutes, cooldown_minutes, device_count,
    node_selector, resource_types, status, exit_query_template_id,
    created_by, created_at, updated_at
) VALUES (
    1, 'CPU Low Usage Scale In', 'Scale in when CPU usage is low', 'pool_exit',
    30.0, 'usage', 40.0,
    0, '', 0,
    'AND', 3, 60, 3,
    '', 'total', 'enabled', 1,
    'admin', datetime('now'), datetime('now')
);

-- 创建策略集群关联
INSERT INTO strategy_cluster_association (strategy_id, cluster_id) VALUES
(1, 1);

-- 创建资源快照数据（连续3天CPU使用率低于30%）
INSERT INTO k8s_cluster_resource_snapshot (
    cluster_id, resource_type, resource_pool,
    max_cpu, max_memory,
    cpu_request, cpu_capacity, mem_request, mem_capacity,
    created_at, updated_at
) VALUES
-- 3天前：CPU 25%
(1, 'total', 'total', 25.0, 45.0, 250.0, 1000.0, 4500.0, 10000.0, datetime('now', '-3 days'), datetime('now', '-3 days')),
-- 2天前：CPU 20%
(1, 'total', 'total', 20.0, 40.0, 200.0, 1000.0, 4000.0, 10000.0, datetime('now', '-2 days'), datetime('now', '-2 days')),
-- 1天前：CPU 28%
(1, 'total', 'total', 28.0, 42.0, 280.0, 1000.0, 4200.0, 10000.0, datetime('now', '-1 days'), datetime('now', '-1 days'));

-- 模拟系统自动生成的订单数据（部分设备匹配）
-- 创建基础订单
INSERT INTO orders (id, order_number, name, description, type, status, created_by, created_at, updated_at) VALUES
(1, 'ESO20241201123461', '策略触发-退池-production-cluster-total-部分匹配', '策略 ''CPU Low Usage Scale In'' 触发退池操作。集群：production-cluster，资源类型：total，需要退池设备：3台，实际匹配：2台。部分设备无法退池，请关注集群资源状态。', 'elastic_scaling', 'pending', 'system/auto', datetime('now'), datetime('now'));

-- 创建弹性伸缩订单详情
INSERT INTO elastic_scaling_order_details (id, order_id, cluster_id, strategy_id, action_type, resource_pool_type, device_count, strategy_triggered_value, strategy_threshold_value, created_at, updated_at) VALUES
(1, 1, 1, 1, 'pool_exit', 'total', 2, 'CPU使用率: 28.0%', 'CPU阈值: 30.0%', datetime('now'), datetime('now'));

-- 创建订单设备关联（只匹配到2台运行中设备）
INSERT INTO order_device (order_id, device_id, status, created_at, updated_at) VALUES
(1, 1001, 'pending', datetime('now'), datetime('now')),
(1, 1004, 'pending', datetime('now'), datetime('now'));

-- 创建策略执行历史记录
INSERT INTO strategy_execution_history (id, strategy_id, cluster_id, resource_type, execution_time, triggered_value, threshold_value, result, order_id, reason, created_at, updated_at) VALUES
(1, 1, 1, 'total', datetime('now'), 'CPU使用率: 28.0%', 'CPU阈值: 30.0%', 'order_created_partial_devices', 1, '连续3天CPU使用率低于30%，部分设备匹配成功，生成退池订单', datetime('now'), datetime('now'));

-- 验证数据插入
SELECT 'Clusters:' as table_name, count(*) as count FROM k8s_cluster
UNION ALL
SELECT 'Devices:', count(*) FROM device
UNION ALL
SELECT 'Query Templates:', count(*) FROM query_template
UNION ALL
SELECT 'Strategies:', count(*) FROM elastic_scaling_strategy
UNION ALL
SELECT 'Strategy Associations:', count(*) FROM strategy_cluster_association
UNION ALL
SELECT 'Resource Snapshots:', count(*) FROM k8s_cluster_resource_snapshot
UNION ALL
SELECT 'Orders:', count(*) FROM orders
UNION ALL
SELECT 'Order Details:', count(*) FROM elastic_scaling_order_details
UNION ALL
SELECT 'Order Devices:', count(*) FROM order_device
UNION ALL
SELECT 'Execution History:', count(*) FROM strategy_execution_history;

-- 显示策略详情
SELECT
    id, name, threshold_trigger_action,
    cpu_threshold_value, cpu_threshold_type,
    duration_minutes, device_count as required_devices, status
FROM elastic_scaling_strategy;

-- 显示设备状态分布
SELECT
    status,
    count(*) as device_count,
    CASE
        WHEN status = 'running' THEN '可用于退池'
        WHEN status = 'in_stock' THEN '库存中'
        WHEN status = 'maintenance' THEN '维护中'
        ELSE '其他'
    END as status_description
FROM device
GROUP BY status
ORDER BY status;

-- 显示资源快照趋势
SELECT
    date(created_at) as snapshot_date,
    max_cpu,
    max_memory,
    CASE
        WHEN max_cpu < 30 THEN 'BREACH'
        ELSE 'NORMAL'
    END as threshold_status
FROM k8s_cluster_resource_snapshot
ORDER BY created_at;

-- 显示生成的订单信息
SELECT
    o.id as order_id,
    o.order_number,
    o.name as order_name,
    o.description,
    o.type,
    o.status,
    o.created_by,
    esd.action_type,
    esd.device_count as matched_devices,
    esd.strategy_triggered_value,
    esd.strategy_threshold_value
FROM orders o
JOIN elastic_scaling_order_details esd ON o.id = esd.order_id;

-- 显示匹配的设备信息
SELECT
    od.order_id,
    od.device_id,
    od.status as device_status,
    d.ci_code,
    d.ip,
    d.arch_type,
    d.cpu,
    d.memory,
    d.status as device_current_status
FROM order_device od
JOIN device d ON od.device_id = d.id
ORDER BY od.order_id, od.device_id;

-- 显示未匹配的设备信息
SELECT
    d.id,
    d.ci_code,
    d.ip,
    d.status,
    CASE
        WHEN d.status = 'in_stock' THEN '已在库存中，无法退池'
        WHEN d.status = 'maintenance' THEN '维护中，无法退池'
        ELSE '其他原因'
    END as reason_not_matched
FROM device d
WHERE d.id NOT IN (SELECT device_id FROM order_device WHERE order_id = 1)
ORDER BY d.id;

-- 显示策略执行历史
SELECT
    seh.id,
    seh.strategy_id,
    seh.cluster_id,
    seh.resource_type,
    seh.triggered_value,
    seh.threshold_value,
    seh.result,
    seh.order_id,
    seh.reason
FROM strategy_execution_history seh;